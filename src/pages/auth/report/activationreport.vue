<template>
    <div>
        <adminheader
          :title="$t('report.activationreport')" :backButton="true" :backTitle="'Back to reports'" :backFunction="back"></adminheader>
        <div class="mx-1 my-2 mr-2">
            <div class="bg-white rounded border p-4">
              <div class="flex items-end gap-2">
               <div class="w-1/2 flex items-end gap-4">
                  <div class="w-1/2">
                    <span class="text-xs">Start Date</span>
                    <DatePicker
                        v-model="startdate"
                        :clearable="true"
                        defaultColor="blue"></DatePicker>
                  </div>
                  <div class="w-1/2">
                    <span class="text-xs">End Date</span>
                      <DatePicker
                      v-model="enddate"
                      :clearable="true"
                      defaultColor="blue"></DatePicker>
                  </div>
                </div>
                <div class="w-1/2 flex gap-2 items-end">
                  <button @click="generateReport" :class="loading ? 'cursor-not-allowed bg-gray-100 text-gray-500' : 'bg-green-500 hover:bg-green-700 text-white'" 
                    class="w-1/2 rounded px-4 py-2" :disabled="loading">
                    {{$t('report.generate')}}
                  </button>
                  <button v-if="lists && lists.length > 0" @click="exportXLSX" :class="loading ? 'cursor-not-allowed bg-gray-100 text-gray-500' : 'bg-blue-500 hover:bg-blue-700 text-white'"
                    class="w-1/2 rounded px-4 py-2" :disabled="loading">
                    Export
                  </button>
                </div>
              </div>
              <div class="border-t border-b py-2">
                <div class="grid grid-cols-8 gap-4 text-xs font-bold">
                  <div>Building</div>
                  <div>Name</div>
                  <div>ActivationDate</div>
                  <div>Plan</div>
                  <div>FreeUsage</div>
                  <div>Price</div>
                  <div>Agent</div>
                  <div>Status</div>
                </div>
              </div>
              <div class="">
                <div class="border-b py-2" v-for="(vp,vi) in lists" :key="`s_ss_${addvu(vi)}`">
                  <div class="grid grid-cols-8 gap-4 text-xs">
                    <div>{{strnum(vi)}}{{vp.address.building || 'The Grand Subang Jaya SS15'}}</div>
                    <div>{{vp.name}}</div>
                    <!-- <div>{{vp.contact}}</div> -->
                    <div>{{formatdate(vp.activationdate)}}</div>
                    <div>{{(packagePlan(vp.plan) && packagePlan(vp.plan).title) || '---'}}</div>
                    <div>{{vp.freeusage}}</div>
                    <div>{{(packagePlan(vp.plan) && packagePlan(vp.plan).price.toFixed(2)) || '--.--' }}</div>
                    <div>{{vp.agent && agents[vp.agent]&& agents[vp.agent].name}}</div>
                    <div>{{vp.statustxt}}</div>
                  </div>
                </div>
              </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions, getPlan, getUserName } from '../../../api'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
    }
  },
  components: {
    adminheader,
    svgCollection,
    DatePicker,
  },
  data () {
    let lists: any = undefined
    let plans: any = {}
    let planids: any = []
    let agentsid: any = []
    let agents: any = {}
    let activeonly: boolean = false
    let startdate: any = ''
    let enddate: any = ''
    return {
        lists,
        plans,
        planids,
        agentsid,
        agents,
        activeonly,
        startdate,
        enddate,
    }
  },
  methods: {
    getAgent (s: string) {
        if (!this.agents[s] && s.trim().length > 0) {
            getUserName({token: this.token, id: s}).then((res: any) => {
                if (res && res.id) {
                    this.agents[res.id] = res
                }
            })
        }
    },
    strnum (p: any) {
      return (p + 1).toString() + '.  '
    },
    getPlans () {
      for (var k = 0; k < this.planids.length; k++ ) {
        if (!this.plans[this.planids[k]]) {
          getPlan({token: this.token, id: this.planids[k]}).then((p: any) => {
            this.plans[p.data.id] = p.data
          })
        }   
      }
    },
    getAgents () {
      for (var k = 0; k < this.agentsid.length; k++ ) {
        if (!this.agents[this.agentsid[k]]) {
          this.getAgent(this.agentsid[k])
          // getPlan({token: this.token, id: this.agentsid[k]}).then((p: any) => {
          //   this.plans[p.data.id] = p.data
          // })
        }   
      }
    },
    formatdate (v: any) {
      return v ? moment(v).format('YYYY-MM-DD') : ''
    },
    packagePlan (p: any) {
      return this.plans[p] ? this.plans[p] : null
    },
    addvu (v: any) {
      return v + 1
    },
    generateReport () {
      // firstget
      this.lists = []
      this.doGetReport()
    },
    exportCSV () {
        let csvRows: any = []
        for (var i = 0; i < this.lists.length; i++) {
            let s: any = this.lists[i]
            csvRows.push(`${i+1},${s.address.building || 'The Grand Subang Jaya SS15'},${s.name},${this.formatdate(s.subscribedate)},${this.formatdate(s.activationdate)}, ${this.plans[s.plan].title}, ${s.freeusage}, ${this.plans[s.plan].price}, ${s.agent && this.agents[s.agent] && this.agents[s.agent].name}, ${s.advancedpayment}`);
        }
                    
        var csvString = csvRows.join("\n");
        let csvFile = new Blob([csvString], { type: "text/csv" });
        let downloadLink = document.createElement("a");
        downloadLink.download = `userList.csv`
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
    },
    doGetReport () {
      let limit: number = 20
      let v1: any = {
        token: this.token, skip: this.lists.length, limit: limit, statustxt: 'active',
        params: [],
      }

      if (this.startdate) {
        v1["activationdatestart"] = moment(this.startdate).format('YYYY-MM-DD')        
        v1["params"].push('activationdatestart')
      }

      if (this.enddate) {
        v1["activationdateend"] = moment(this.enddate).format('YYYY-MM-DD')        
        v1["params"].push('activationdateend')
      }

      getSubscriptions(v1).then((res: any) => {
        this.lists= this.lists.concat(res.data)
        for (var k = 0; k < res.data.length; k++ ) {
          this.planids.push(res.data[k].plan)
          this.agentsid.push(res.data[k].agent)
        }
        this.planids = [...new Set(this.planids)]
        this.agentsid = [...new Set(this.agentsid)]
        this.getPlans()
        this.getAgents()
        if (res && res.total > this.lists.length) {
          setTimeout(this.doGetReport, 300)
        }
      })
    },
    back () {
        this.$router.back()
    },
    formatDate (p: any) {
        return (p&&moment(p).format('YYYY-MM-DD')) || '0000-00-00'
    },
    copy (p: any) {
        crossStore.SetNotmsg({
          title: this.$t('c.textcopied'),
          msg: p,
          type: 'success',
        })
        navigator.clipboard.writeText(p)
    },
  }
})
</script>